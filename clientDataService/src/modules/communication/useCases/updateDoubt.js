import CommunicationRepository from '../repositories/communicationRepository.js';
import { LessonMetricModel } from '../../../domain/models/metrics/lessonMetricsModel.js';
import { ValidationError, AuthorizationError, NotFoundError } from '../../../utils/customErrors/index.js';

const communicationRepository = new CommunicationRepository();

export const updateDoubt = async ({
  userId,
  role,
  commentId,
  status
}) => {
  try {
    console.log(`DEBUG - updateDoubt called with:`, { userId, role, commentId, status, commentIdType: typeof commentId, statusType: typeof status });

    if (!userId) {
      throw new ValidationError('userId is required');
    }

    if (!commentId) {
      throw new ValidationError('commentId is required');
    }

    if (status === undefined || status === null || ![-1, 0, 1].includes(status)) {
      throw new ValidationError('status must be -1, 0, or 1');
    }

    // Find the lesson metric that contains this doubt
    // Convert commentId to string to match the schema
    const commentIdStr = commentId.toString();
    console.log(`DEBUG - Searching for doubt with commentId: ${commentIdStr}`);

    const lessonMetric = await LessonMetricModel.findOne({
      'doubts.commentId': commentIdStr
    });

    if (!lessonMetric) {
      console.log(`DEBUG - No lesson metric found for commentId: ${commentIdStr}`);
      throw new NotFoundError('Doubt not found in lesson metrics');
    }

    console.log(`DEBUG - Found lesson metric with ${lessonMetric.doubts.length} doubts`);
    console.log(`DEBUG - Doubts in metric:`, lessonMetric.doubts.map(d => ({ commentId: d.commentId, status: d.status })));

    // Find the specific doubt in the array
    const doubtIndex = lessonMetric.doubts.findIndex(doubt => doubt.commentId === commentIdStr);
    const doubt = lessonMetric.doubts[doubtIndex];

    if (!doubt) {
      console.log(`DEBUG - Doubt not found in array for commentId: ${commentIdStr}`);
      throw new NotFoundError('Doubt not found');
    }

    const currentStatus = doubt.status;
    console.log(`DEBUG - Current doubt status: ${currentStatus}, requested status: ${status}`);

    if (currentStatus === status) {
      throw new ValidationError(`Doubt is already in status ${status}`);
    }

    // Update the doubt status
    console.log(`DEBUG - Updating doubt status from ${currentStatus} to ${status}`);
    lessonMetric.doubts[doubtIndex].status = status;
    await lessonMetric.save();
    console.log(`DEBUG - Successfully updated doubt status in database`);

    // If status is 1 (resolved), find and delete the communication channel
    if (status === 1) {
      const channels = await communicationRepository.getChannelsByContext({
        contextType: 'DOUBT',
        contextId: commentId
      });

      if (channels.length > 0) {
        const channel = channels[0];
        
        // Verify user has permission to resolve the doubt (must be channel member)
        const isMember = channel.members.some(member => member._id.toString() === userId.toString());
        if (!isMember) {
          throw new AuthorizationError('Not authorized to resolve this doubt');
        }

        // Deactivate the channel
        await communicationRepository.updateChannel(channel._id, { isInactive: true });
      }
    }

    const result = {
      success: true,
      message: 'Doubt status updated successfully',
      data: {
        commentId,
        previousStatus: currentStatus,
        newStatus: status,
        updatedAt: new Date()
      }
    };

    console.log(`DEBUG - updateDoubt returning success:`, result);
    return result;

  } catch (error) {
    if (error instanceof ValidationError || error instanceof AuthorizationError || error instanceof NotFoundError) {
      throw error;
    }
    
    console.error('Unexpected error in updateDoubt:', error);
    throw new Error(`Failed to update doubt: ${error.message}`);
  }
};
