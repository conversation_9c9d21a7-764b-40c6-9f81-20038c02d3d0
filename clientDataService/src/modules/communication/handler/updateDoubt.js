import connectToDB from '../../../infra/libs/mongodb/connect.js';
import { apiResponse } from '../../../utils/apiResponse.js';
import { updateDoubt } from '../useCases/updateDoubt.js';

export async function handler(event) {
  try {
    console.log(`DEBUG - updateDoubt handler called with event:`, {
      pathParameters: event.pathParameters,
      body: event.body,
      authorizer: event.requestContext?.authorizer
    });

    await connectToDB();

    const { userId, id, role } = event.requestContext.authorizer;
    const actualUserId = userId || id;

    if (!actualUserId) {
      return apiResponse(401, { error: 'Unauthorized: User ID not found' });
    }

    const { commentId } = event.pathParameters;
    if (!commentId) {
      return apiResponse(400, { error: 'commentId is required' });
    }

    let body;
    try {
      body = typeof event.body === 'string' ? JSON.parse(event.body) : event.body;
    } catch (error) {
      return apiResponse(400, { error: 'Invalid JSON in request body' });
    }

    const { status } = body;
    if (status === undefined || status === null) {
      return apiResponse(400, { error: 'status is required' });
    }

    console.log(`DEBUG - updateDoubt handler parsed data:`, { actualUserId, role, commentId, status });

    // Update doubt status
    const result = await updateDoubt({
      userId: actualUserId,
      role,
      commentId,
      status
    });

    console.log(`DEBUG - updateDoubt handler got result:`, result);
    return apiResponse(200, { body: result });

  } catch (error) {
    console.error('Error in updateDoubt handler:', error);
    
    if (error.name === 'ValidationError') {
      return apiResponse(400, { error: error.message });
    }
    
    if (error.name === 'AuthorizationError') {
      return apiResponse(403, { error: error.message });
    }
    
    if (error.name === 'NotFoundError') {
      return apiResponse(404, { error: error.message });
    }
    
    return apiResponse(500, { error: 'Internal server error' });
  }
}
