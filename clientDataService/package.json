{"name": "clientdataservice", "version": "1.0.0", "description": "", "main": "index.js", "type": "module", "scripts": {"test-integration": "jest --testMatch '**/*.integration.test.js'", "startdev": "nodemon --exec babel-node ./src/app.js", "build": "npm run lint && npm test", "lint": "eslint .", "test": "jest --passWithNoTests", "install:layers": "cd layers/database/nodejs && npm install && cd ../../auth/nodejs && npm install && cd ../../express/nodejs && npm install && cd ../../..", "fetch-logins": "node ./@scripts/fetchUniqueLogins.js", "relatorio-diario": "node ./@scripts/relatorioDiario.js", "top-student-per-school": "node ./@scripts/topStudentPerSchool.js", "school-overall-check": "node ./@scripts/schoolOverallCheck.js", "give-atoms-to-users": "node ./@scripts/giveAtomsToUsers.js", "deploy:safe": "./scripts/safe-deploy.sh dev", "deploy:safe:staging": "./scripts/safe-deploy.sh staging", "deploy:safe:prod": "./scripts/safe-deploy.sh production", "pre-deploy-check": "./scripts/pre-deploy-check.sh dev", "stack:status": "aws cloudformation describe-stacks --stack-name cds-service-dev --profile atomize-dev --region us-east-2 --query 'Stacks[0].StackStatus' --output text", "stack:delete": "aws cloudformation delete-stack --stack-name cds-service-dev --profile atomize-dev --region us-east-2", "stack:events": "aws cloudformation describe-stack-events --stack-name cds-service-dev --profile atomize-dev --region us-east-2 --query 'StackEvents[:10].[Timestamp,LogicalResourceId,ResourceStatus,ResourceStatusReason]' --output table", "get:invoke-url": "aws cloudformation describe-stacks --stack-name cds-service-dev --profile atomize-dev --region us-east-2 --query \"Stacks[0].Outputs[?OutputKey=='ServiceEndpoint'].OutputValue\" --output text", "create-students-from-csv": "node ./@scripts/createStudentsFromCSV.js", "validate-data": "node scripts/validateDataIntegrity.js", "validate-data:fix": "node scripts/validateDataIntegrity.js --fix", "validate-data:dry-run": "node scripts/validateDataIntegrity.js --dry-run", "inspect-metrics": "node scripts/inspectLessonMetrics.js", "optimize-indexes": "node scripts/optimizeIndexes.js", "optimize-indexes:dry-run": "node scripts/optimizeIndexes.js --dry-run", "optimize-indexes:force": "node scripts/optimizeIndexes.js --force", "e2e-validate": "node scripts/endToEndValidation.js"}, "repository": {"type": "git", "url": "git+https://github.com/Atomize-dev/clientDataService.git"}, "resolutions": {"path-to-regexp": "8.0.0"}, "author": "", "license": "ISC", "bugs": {"url": "https://github.com/Atomize-dev/clientDataService/issues"}, "homepage": "https://github.com/Atomize-dev/clientDataService#readme", "dependencies": {"@aws-sdk/client-lambda": "^3.616.0", "@aws-sdk/client-sns": "^3.664.0", "@aws-sdk/client-sqs": "^3.677.0", "@types/dompurify": "^3.0.5", "aws-sdk": "^2.1659.0", "axios": "^1.9.0", "babel-cli": "^6.26.0", "babel-node": "^0.0.1-security", "bcrypt": "^5.1.1", "bcryptjs": "^2.4.3", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.19.2", "isomorphic-dompurify": "^2.26.0", "jsonwebtoken": "^9.0.2", "marked": "^16.1.1", "mongoose": "^8.5.1", "nodemon": "^3.1.4", "openai": "^4.91.1", "serverless-http": "^3.2.0", "uuid": "^10.0.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.4", "babel-cli": "^6.26.0", "babel-jest": "^29.7.0", "babel-loader": "^9.2.1", "babel-node": "^0.0.1-security", "copy-webpack-plugin": "^12.0.2", "csv-parser": "^3.2.0", "eslint": "^9.13.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jest": "^28.8.3", "jest": "^29.7.0", "mongodb-memory-server": "^10.2.0", "nodemon": "^3.1.4", "serverless-deployment-bucket": "^1.6.0", "serverless-domain-manager": "^8.0.0", "serverless-export-env": "^2.2.0", "serverless-iam-roles-per-function": "^3.2.0", "serverless-offline": "^14.4.0", "serverless-plugin-split-stacks": "^1.14.0", "serverless-plugin-warmup": "^8.3.0", "serverless-prune-plugin": "^2.1.0", "serverless-step-functions": "^3.21.2", "serverless-webpack": "^5.15.0", "supertest": "^7.0.0", "webpack": "^5.97.1", "webpack-node-externals": "^3.0.0"}}