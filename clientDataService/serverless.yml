service: cds-service

frameworkVersion: "4" # using node 20.x 

plugins:
  - serverless-export-env
  - serverless-prune-plugin
  - serverless-iam-roles-per-function
  - serverless-step-functions
  - serverless-plugin-warmup
  - serverless-plugin-split-stacks
  - serverless-deployment-bucket
  - serverless-offline

custom:
  serverless-iam-roles-per-function:
    defaultInherit: true
  cronEnable:
    dev: true
    staging: true
    production: true
  stage: ${opt:stage, 'dev'}
  service: 'cds-service'
  awsId: "************"
  warmupEnable:
    dev: true
    staging: true
    production: true
  build:
    esbuild: false # Disable ESBuild to avoid conflicts with webpack
  BASE_SQS_QUEUE_URL: https://sqs.${self:provider.region}.amazonaws.com/${self:custom.awsId}/${self:service}-${self:custom.stage}-queue
  BASE_SQS_QUEUE_ARN: arn:aws:sqs:${self:provider.region}:${self:custom.awsId}:${self:service}-${self:custom.stage}-queue

package:
  patterns:
    - '!.git/**'
    - '!.gitignore'
    - '!.DS_Store'
    - '!README.md'
    - '!docs/**'
    - '!test/**'
    - '!tests/**'
    - '!__tests__/**'
    - '!*.test.js'
    - '!*.spec.js'
    - '!jest.config.js'
    - '!coverage/**'
    - '!node_modules/.cache/**'
    - '!node_modules/**/*.md'
    - '!node_modules/**/*.txt'
    - '!node_modules/**/*.ts'
    - '!node_modules/**/*.map'
    - '!node_modules/**/test/**'
    - '!node_modules/**/tests/**'
    - '!node_modules/**/__tests__/**'
    - '!layers/**/test/**'
    - '!layers/**/tests/**'
    - '!layers/**/__tests__/**'
    - '!*.tar'
    - '!*.zip'
    - '!*.gz'
    - '!archive.tar'
    - '!function.zip'
    - '!deployment-backups/**'
    - '!@mocks/**'
    - '!@scripts/**'
    - '!scripts/**'
    - '!.serverless/**'
    - '!debug_*.js'
    - '!debug_*.md'
    - '!investigate_*.js'
    - '!monitor_*.js'
    - '!solution_*.js'
    - '!test_*.js'
    - '!*.csv'
    - '!eslint.config.js'
    - '!webpack.config.js'
    - '!node_modules/typescript/**'
    - '!node_modules/java-invoke-local/**'
  excludeDevDependencies: true

provider:
  name: aws
  runtime: nodejs20.x
  region: us-east-2
  profile: atomize-dev
  stage: ${opt:stage, 'dev'}
  apiGateway:
    binaryMediaTypes:
      - 'multipart/form-data'
      - 'image/*'
      - 'application/octet-stream'
  environment:
    BASE_SQS_QUEUE_NAME: ${self:service}-${self:custom.stage}-queue-
    BASE_SQS_QUEUE_ARN: ${self:custom.BASE_SQS_QUEUE_ARN}
    BASE_SQS_QUEUE_URL: ${self:custom.BASE_SQS_QUEUE_URL}
    MATRIXIS_SQS_QUEUE_URL: https://sqs.${self:provider.region}.amazonaws.com/${self:custom.awsId}/matrixis-service-${self:custom.stage}
    MATRIXIS_SQS_QUEUE_ARN: arn:aws:sqs:${self:provider.region}:${self:custom.awsId}:matrixis-service-${self:custom.stage}-queue
    MATRIXIS_API_URL: ${ssm:/matrixis-${self:custom.stage}-api-url}
    SHOP_QUEUE_URL: ${self:custom.BASE_SQS_QUEUE_URL}/shop-queue
    SHOP_QUEUE_ARN: ${self:custom.BASE_SQS_QUEUE_ARN}/shop-queue
    BASE_STATE_MACHINE_ARN: arn:aws:states:${self:provider.region}:${self:custom.awsId}:stateMachine:${self:service}-${self:custom.stage}-state-machine-
    BASE_STATE_MACHINE_NAME: ${self:service}-${self:custom.stage}-state-machine-
    BASE_LAMBDA_ARN: arn:aws:lambda:${self:provider.region}:${self:custom.awsId}:function:${self:service}-${self:custom.stage}-
    STAGE: ${self:custom.stage}
    SERVICE: cds-service
    AWS_ACCOUNT_ID: "************"
    DATABASE_URL: ${ssm:/qaas-database-url}
    CONNECTION_STRING: ${ssm:/${self:custom.stage}-mongodb-url}
    SAPS_API_URL: ${ssm:/saps-${self:custom.stage}-api-url}
    REGION: us-east-2
    S3_SECRET_ACCESS_KEY: ${ssm:/s3-secret-access-key}
    S3_ACCESS_KEY_ID: ${ssm:/s3-access-key-id}
    S3_BUCKET_NAME: qaas-service-prod-qaas-s3
    S3_ENDPOINT: ${ssm:/s3-endpoint}
    NPM_ATOM_OLYMPIADS_TOKEN: ${ssm:/npm-atom-olympiads-token}
    JWT_SECRET: ${ssm:/${self:custom.stage}-service-jwt-secret}
    OPENAI_API_KEY: ${ssm:/openai-${self:custom.stage}-api-key}
    # OAuth Configuration
    GOOGLE_CLIENT_ID: ${ssm:/google-${self:custom.stage}-client-id}
    GOOGLE_CLIENT_SECRET: ${ssm:/google-${self:custom.stage}-client-secret}
    FRONTEND_LOGIN_URL: ${ssm:/frontend-${self:custom.stage}-login-url}
  deploymentBucket:
    name: ${self:service}-${self:custom.stage}-deployment-bucket
  iam:
    role:
      statements:
        - Effect: Allow
          Action:
            - scheduler:CreateSchedule
            - scheduler:DeleteSchedule
            - scheduler:GetSchedule # Added missing permission
            - scheduler:UpdateSchedule # Added missing permission
          Resource: "*"
        - Effect: Allow
          Action:
            - s3:GetObject
            - s3:PutObject
            - s3:DeleteObject
            - s3:PutObjectAcl
          Resource: 
            - "arn:aws:s3:::communication-${self:custom.stage}-bucket/*"
        - Effect: Allow
          Action:
            - s3:ListBucket
            - s3:HeadBucket
          Resource: 
            - "arn:aws:s3:::communication-${self:custom.stage}-bucket"
# layers:
#   database:
#     path: layers/database
#     name: ${self:service}-${self:provider.stage}-database
#     description: MongoDB and Mongoose dependencies
#     compatibleRuntimes:
#       - nodejs18.x
#       - nodejs20.x
#     package:
#       include:
#         - node_modules/**
#   auth:
#     path: layers/auth
#     name: ${self:service}-${self:provider.stage}-auth
#     description: Authentication and authorization dependencies
#     compatibleRuntimes:
#       - nodejs18.x
#       - nodejs20.x
#     package:
#       include:
#         - node_modules/**
#   express:
#     path: layers/express
#     name: ${self:service}-${self:provider.stage}-express
#     description: Express and related middleware dependencies
#     compatibleRuntimes:
#       - nodejs18.x
#       - nodejs20.x
#     package:
#       include:
#         - node_modules/**

functions:
  - ${file(sls/functions/statistics/battle/index.yml)}
  - ${file(sls/functions/paths/index.yml)}
  - ${file(sls/functions/contract/index.yml)}
  - ${file(sls/functions/groups/index.yml)}
  - ${file(sls/functions/schools/index.yml)}
  - ${file(sls/functions/studentActivity/index.yml)}
  - ${file(sls/functions/management/index.yml)}
  - ${file(sls/functions/token/index.yml)}
  # - ${file(sls/functions/recomendations/index.yml)}
  - ${file(sls/functions/user/index.yml)}
  - ${file(sls/functions/questionMetrics/index.yml)}  
  - ${file(sls/functions/school/index.yml)}
  - ${file(sls/functions/middleware/index.yml)}
  - ${file(sls/functions/flashcards/flashcard/index.yml)}
  - ${file(sls/functions/flashcards/decks/index.yml)}
  - ${file(sls/functions/shop/index.yml)}
  - ${file(sls/functions/calendar/index.yml)}
  - ${file(sls/functions/communication/index.yml)}
  - ${file(sls/functions/oauth/index.yml)}
  - ${file(sls/functions/fileManager/index.yml)}

resources:
  - ${file(sls/functions/statistics/battle/queue.yml)}
  - ${file(sls/functions/shop/queue.yml)}
  - ${file(sls/functions/communication/resources.yml)}
