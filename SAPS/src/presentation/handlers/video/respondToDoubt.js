import { apiResponse } from '../../../../utils/apiResponse.js';
import { respondToDoubtService } from '../../../application/services/video/respondToDoubtService.js';

export async function handler(event) {
  try {
    const authHeader = event.headers?.Authorization || event.headers?.authorization;
    if (!authHeader) {
      return apiResponse(401, { body: { message: 'Missing Authorization header' } });
    }

    const { commentId } = event.pathParameters;
    if (!commentId) {
      return apiResponse(400, { body: { message: 'commentId is required' } });
    }

    let body = {};
    if (event.body) {
      try {
        body = JSON.parse(event.body);
      } catch (error) {
        // Body is optional, so we'll just use empty object
      }
    }

    const result = await respondToDoubtService({
      commentId: parseInt(commentId),
      teacherId: event.requestContext.authorizer.id,
      teacherName: body.teacherName || 'Professor',
      token: authHeader
    });

    return apiResponse(200, { body: result });
  } catch (error) {
    console.log('respondToDoubt handler error', { 
      message: error.message, 
      stack: error.stack,
      commentId: event.pathParameters?.commentId 
    });
    return apiResponse(error.status || error.statusCode || 500, { 
      body: { message: error.message || 'Internal server error' } 
    });
  }
}
